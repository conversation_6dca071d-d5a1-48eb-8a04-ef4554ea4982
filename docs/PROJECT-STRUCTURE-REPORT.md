# Rapport de Structure et de Logique du Projet HeliosETL

## Table des Matières
1. [Aperçu du Projet](#aperçu-du-projet)
2. [Structure du Projet](#structure-du-projet)
3. [Modèles de Données](#modèles-de-données)
4. [Architecture des Services](#architecture-des-services)
5. [Architecture de la Base de Données](#architecture-de-la-base-de-données)
6. [Système de Configuration](#système-de-configuration)
7. [Système de Journalisation](#système-de-journalisation)
8. [Conception du Pipeline ETL](#conception-du-pipeline-etl)
9. [État d'Implémentation](#état-dimplémentation)
10. [Directives de Développement](#directives-de-développement)

## Aperçu du Projet

**HeliosETL** est une application console C# conçue pour automatiser le processus d'Extraction, de Transformation et de Chargement (ETL) pour la migration des données de Helios v1 vers Helios v2. L'application suit une architecture modulaire avec une séparation claire des préoccupations et implémente des modèles C# modernes.

### Fonctionnalités Clés
- **Pipeline ETL Modulaire** : Extracteurs, transformateurs et chargeurs séparés
- **Support de Doubles Bases de Données** : SQL Server (source) et MySQL (destination)
- **Interface Console Riche** : Spectre.Console pour une sortie terminal conviviale
- **Journalisation Complète** : Journalisation de fichiers basée sur Serilog avec niveaux configurables
- **Configuration YAML** : Gestion de configuration flexible
- **Modèle de Référentiel** : Abstraction propre de la couche d'accès aux données
- **Services Singleton** : Gestion centralisée des services

### Stack Technologique
- **.NET 9.0** : Framework .NET le plus récent
- **Serilog** : Journalisation structurée
- **Spectre.Console** : Interface terminal riche
- **YamlDotNet** : Analyse de configuration YAML
- **Microsoft.Data.SqlClient** : Connectivité SQL Server
- **MySql.Data** : Connectivité MySQL

## Structure du Projet

```
HeliosETL/
├── Const/                          # Application constants
│   └── Error.cs                    # Error message constants
├── Models/                         # Data models and DTOs
│   ├── Configuration/              # Configuration models
│   │   ├── AppConfig.cs           # Main application configuration
│   │   ├── DatabaseConfig.cs      # Database connection settings
│   │   └── LoggingConfig.cs       # Logging configuration
│   ├── v1/                        # Helios v1 data models (source)
│   │   ├── Tickets.cs             # Ticket entity
│   │   ├── TicketsHistorique.cs   # Ticket history entity
│   │   └── Categorie.cs           # Category entity
│   └── v2/                        # Helios v2 data models (destination)
│       ├── Issue.cs               # Issue entity (maps from Tickets)
│       ├── Journal.cs             # Journal entity (maps from TicketsHistorique)
│       ├── DomaineMetier.cs       # Business domain entity
│       ├── Activite.cs            # Activity entity
│       ├── Mission.cs             # Mission entity
│       ├── TypeMission.cs         # Mission type entity
│       ├── IssuePriorite.cs       # Issue priority entity
│       └── IssueStatut.cs         # Issue status enumeration
├── Repositories/                   # Data access layer
│   ├── IRepository.cs             # Generic repository interface
│   ├── Repository.cs              # Base repository implementation
│   ├── ETLHistoryRepository.cs    # ETL history tracking (SQLite)
│   ├── v1/                        # Helios v1 repositories (SQL Server)
│   │   ├── TicketsRepository.cs   # Tickets data access
│   │   ├── TicketHistoriqueRepository.cs # Ticket history data access
│   │   └── CategorieRepository.cs # Category data access
│   └── v2/                        # Helios v2 repositories (MySQL)
│       ├── IssueRepository.cs     # Issues data access
│       ├── JournalRepository.cs   # Journal data access
│       ├── IssuePieceJointeRepository.cs # Issue attachments data access
│       ├── IssuePrioriteRepository.cs # Issue priority data access
│       ├── MissionRepository.cs   # Mission data access
│       ├── TypeMissionRepository.cs # Mission type data access
│       ├── DomaineMetierRepository.cs # Business domain data access
│       ├── PersonneRepository.cs  # Person data access
│       └── TypePersonneRepository.cs # Person type data access
├── Services/                      # Business logic and utilities
│   ├── ConfigurationService.cs   # Configuration management
│   ├── LoggingService.cs          # Logging service
│   ├── Database.cs                # Database connection management
│   ├── CachedMemory.cs            # In-memory data cache
│   ├── Extractors/                # Data extraction services
│   │   ├── IExtractor.cs          # Extractor interface
│   │   ├── TicketsExtractor.cs    # Tickets extraction logic
│   │   ├── TicketHistoryExtractor.cs # History extraction logic
│   │   └── PoleExtractor.cs       # Pole extraction logic
│   ├── Transformers/              # Data transformation services
│   │   ├── ITransformer.cs        # Transformer interface
│   │   ├── TicketToIssueTransformer.cs # Ticket→Issue transformation
│   │   ├── TicketHistoryToJournalTransformer.cs # History→Journal transformation
│   │   ├── CategoriesToMissionTransformer.cs # Category→Mission transformation
│   │   └── PoleToDomaineMetierTransformer.cs # Pole→Domain transformation
│   ├── Loaders/                   # Data loading services
│   │   ├── IssueLoader.cs         # Issue loading logic
│   │   ├── JournalLoader.cs       # Journal loading logic
│   │   ├── MissionLoader.cs       # Mission loading logic
│   │   └── DomaineMetierLoader.cs # Domain loading logic
│   └── Validators/                # Data validation services
│       ├── IValidator.cs          # Validator interface
│       ├── TicketValidator.cs     # Ticket validation logic
│       ├── IssueValidator.cs      # Issue validation logic
│       ├── JournalValidator.cs    # Journal validation logic
│       ├── MissionValidator.cs    # Mission validation logic
│       ├── DomaineMetierValidator.cs # Domain validation logic
│       └── TicketHistoryValidator.cs # History validation logic
├── docs/                          # Documentation
│   ├── README-Configuration.md    # Configuration guide
│   ├── README-Logging.md          # Logging guide
│   └── PROJECT-STRUCTURE-REPORT.md # This report
├── config.example.yaml            # Configuration template
├── config.yaml                    # Actual configuration (gitignored)
├── Program.cs                     # Application entry point
├── HeliosETL.csproj              # Project file
└── HeliosETL.sln                 # Solution file
```

## Modèles de Données

### Modèles Helios v1 (Système Source)
Les modèles v1 représentent la structure du système hérité :

**Tickets** - Entité principale de ticket avec 30 propriétés incluant :
- Informations de base : `idTickets`, `ct_num`, `client`, `dateCreation`
- Assignation : `demandeur`, `assigne`, `pole`
- Classification : `type`, `status`, `priorite`, `niveau`
- Catégories : `categorie`, `categorie2`, `categorie3`
- Chronométrage : `tempsTotal`, `dateRappel`, `dateResolution`
- Contenu : `titre`, `description`

**TicketsHistorique** - Entrées d'historique/journal des tickets :
- Liens vers les tickets via `idTickets`
- Suivi des modifications avec `datModificationn`, `correspondant`
- Contient `description`, `temps` (temps passé)
- Indicateurs : `noteInterne`, `pieceJointe`, `envoiEmail`

**Categorie** - Structure simple de catégorie :
- `libelle` (étiquette) et `description`

### Modèles Helios v2 (Système de Destination)
Les modèles v2 représentent le système modernisé :

**Issue** - Représentation améliorée du ticket :
- Identité basée sur OID avec versionnement (`oid`, `__version`)
- Métadonnées riches : `code`, `sujet`, `description`
- Datation complète : création, modification, début/fin planifiés/effectifs
- Énumérations de priorité et de statut
- Relations hiérarchiques (problèmes parent/enfant)
- Suivi du temps : minutes estimées vs. effectives
- Relations avec les activités et les journaux

**Journal** - Suivi d'historique modernisé :
- Liens vers les problèmes, prend en charge les notes privées/internes
- Structure simplifiée axée sur les données essentielles

**DomaineMetier** - Classification des domaines d'activité :
- Remplace le concept de pôle de v1
- Prend en charge le suivi d'obsolescence

**Activite** - Gestion d'activité/projet :
- Liens vers les domaines d'activité
- Suivi complet du cycle de vie du projet

**Mission** & **TypeMission** - Gestion de mission :
- Structure de mission hiérarchique
- Classification basée sur le type avec associations de domaine

## Architecture des Services

### Services de Base

**ConfigurationService** (Singleton)
- Gestion de configuration basée sur YAML
- Implémentation singleton thread-safe
- Rechargement automatique de la configuration
- Modèles de configuration fortement typés

**LoggingService** (Singleton)
- Journalisation structurée basée sur Serilog
- Journalisation uniquement par fichier (le terminal utilise Spectre.Console)
- Niveaux de journalisation et rétention configurables
- Journalisation de secours pour les échecs de configuration

**Database**
- Gestion de connexion à double base de données (SQL Server + MySQL)
- Génération de chaîne de connexion à partir de la configuration
- Test et validation de connexion
- Gestion appropriée du cycle de vie de connexion

**CachedMemory** (Singleton)
- Cache de données en mémoire pour les opérations ETL
- Collections séparées pour les entités v1 et v2
- Optimise l'accès aux données pendant la transformation

### Composants ETL

**Extracteurs** (`IExtractor`)
- Interface d'extraction générique avec trois méthodes :
  - `Extract<T>()` - Extraire toutes les entités
  - `Extract<T>(string query)` - Extraction basée sur requête
  - `Extract<T>(int id)` - Extraction d'entité unique
- Implémentations : `TicketsExtractor`, `TicketHistoryExtractor`, `PoleExtractor`

**Transformateurs** (`ITransformer`)
- Processus de transformation en cinq phases :
  - `Extract<T>()` - Extraction de données
  - `Prepare<T>()` - Préparation de données
  - `Transform<T>()` - Transformation de données
  - `Validate()` - Validation de données
  - `Load()` - Chargement de données
- Transformateurs clés :
  - `TicketToIssueTransformer` - Migration de ticket principale
  - `TicketHistoryToJournalTransformer` - Migration d'historique
  - `CategoriesToMissionTransformer` - Mappage de catégorie à mission
  - `PoleToDomaineMetierTransformer` - Mappage de pôle à domaine

**Chargeurs**
- Logique de chargement spécialisée pour chaque entité v2
- Gestion de l'insertion de données spécifique à MySQL
- Gestion des relations et des clés étrangères

**Validateurs**
- Assurance qualité des données avant chargement
- Validation des règles métier
- Vérifications d'intégrité référentielle

## Architecture de la Base de Données

### Implémentation du Modèle de Référentiel

**IRepository** - Interface de référentiel générique :
```csharp
public interface IRepository
{
    HashSet<T> GetAll<T>();
    T GetById<T>(int id);
    bool Add<T>(T entity);
    bool Update<T>(T entity);
    bool Delete<T>(T entity);
    bool DeleteById<T>(int id);
}
```

**Repository** - Classe de référentiel de base :
- Gère l'initialisation de connexion à la base de données
- Fournit l'infrastructure de journalisation
- Traite les préoccupations communes de référentiel

**Référentiels Spécialisés** :

**v1 (SQL Server)** :
- `TicketsRepository` - Opérations SQL Server pour les tickets v1
- `TicketHistoriqueRepository` - Opérations SQL Server pour l'historique v1
- `CategorieRepository` - Accès aux données de catégorie

**v2 (MySQL)** :
- `IssueRepository` - Opérations MySQL pour les problèmes v2
- `JournalRepository` - Opérations MySQL pour les journaux v2
- `IssuePieceJointeRepository` - Opérations MySQL pour les pièces jointes
- `IssuePrioriteRepository` - Opérations MySQL pour les priorités d'issues
- `MissionRepository` - Opérations MySQL pour les missions
- `TypeMissionRepository` - Opérations MySQL pour les types de mission
- `DomaineMetierRepository` - Opérations MySQL pour les domaines métier
- `PersonneRepository` - Opérations MySQL pour les personnes
- `TypePersonneRepository` - Opérations MySQL pour les types de personne

**Autres** :
- `ETLHistoryRepository` - Opérations SQLite pour l'historique ETL

### Connexions de Base de Données

**SQL Server (Source)**
- Se connecte à la base de données Helios v1
- Opérations en lecture seule pour l'extraction de données
- Chaîne de connexion : `Data Source={host};Initial Catalog={db};User ID={user};Password=******;TrustServerCertificate=true`

**MySQL (Destination)**
- Se connecte à la base de données Helios v2
- Opérations d'écriture pour le chargement de données
- Chaîne de connexion : `Server={host};Database={db};Uid={user};Pwd=******;`

## Système de Configuration

### Configuration Basée sur YAML
L'application utilise un système de configuration YAML hiérarchique :

```yaml
database:
  sqlServer:
    dataSource: "server-host"
    initialCatalog: "database-name"
    userID: "username"
    password: "password"
  mySQL:
    server: "mysql-host"
    database: "database-name"
    userID: "username"
    password: "password"

logging:
  console:
    enabled: false
    minimumLevel: "Information"
  file:
    enabled: true
    minimumLevel: "Information"
    logDirectory: "logs"
    fileNameTemplate: "helios-etl-{Date}.log"
    retainedFileCountLimit: 30
    fileSizeLimitBytes: 10485760
```

### Modèles de Configuration
- **AppConfig** - Conteneur de configuration racine
- **DatabaseConfig** - Paramètres de connexion à la base de données
- **LoggingConfig** - Configuration de journalisation avec paramètres console et fichier
- **SqlServerConfig** / **MySqlConfig** - Paramètres spécifiques à la base de données

## Système de Journalisation

### Intégration Serilog
- **Journalisation Fichier Uniquement** : Tous les logs vont aux fichiers, le terminal utilise Spectre.Console
- **Journalisation Structurée** : Support pour les messages de log paramétrés
- **Niveaux de Log** : Verbose, Debug, Information, Warning, Error, Fatal
- **Rotation Automatique** : Rotation quotidienne avec limites de taille (10Mo par défaut)
- **Rétention** : Période de rétention configurable (30 jours par défaut)

### Modèles d'Utilisation
```csharp
private readonly ILogger _logger = LoggingService.Instance.GetLogger<MyClass>();

_logger.Information("Traitement de {RecordCount} enregistrements", count);
_logger.Error(ex, "Échec du traitement de l'enregistrement {RecordId}", id);
```

## Conception du Pipeline ETL

### Flux de Migration de Données

1. **Tickets → Issues**
   - Extraction des tickets depuis SQL Server
   - Transformation des propriétés de ticket au format problème
   - Mappage des catégories aux activités/missions
   - Gestion des mappages de statut et de priorité
   - Chargement dans la table Issues MySQL

2. **TicketsHistorique → Journal**
   - Extraction de l'historique des tickets depuis SQL Server
   - Transformation au format journal
   - Liaison aux problèmes correspondants
   - Préservation des informations de chronométrage et de notes
   - Chargement dans la table Journal MySQL

3. **Categories → Missions**
   - Extraction des hiérarchies de catégories
   - Transformation en structure de mission
   - Création de types de mission et de relations
   - Chargement dans les tables Mission MySQL

4. **Poles → DomaineMetier**
   - Extraction des informations de pôle
   - Transformation au format domaine métier
   - Chargement dans la table DomaineMetier MySQL

### Logique de Transformation

Le processus de transformation principal (tel que documenté dans `TicketToIssueTransformer`) :

1. **Extraire** tous les tickets et l'historique des tickets
2. **Valider** l'intégrité des données source
3. **Transformer** les données au format v2 :
   - Entités Issue à partir des tickets
   - Entrées de journal à partir de l'historique
   - DomaineMetier à partir des pôles
   - Entités Activite
   - Structures de Mission
4. **Charger** les données transformées dans MySQL

## État d'Implémentation

### ✅ Composants Terminés

**Infrastructure** :
- Service de configuration avec support YAML
- Service de journalisation avec intégration Serilog
- Gestion de connexion à la base de données
- Implémentation du modèle de référentiel
- Gestion des erreurs et constantes

**Modèles de Données** :
- Définitions complètes des modèles v1 et v2
- Modèles de configuration
- Définitions d'énumérations avec descriptions

**Services de Base** :
- ConfigurationService (entièrement implémenté)
- LoggingService (entièrement implémenté)
- Service Database (entièrement implémenté)
- Service CachedMemory (structure complète)

**Référentiels** :
- Interface IRepository
- Classe Repository de base
- ETLHistoryRepository (opérations CRUD implémentées)

**v1 Repositories** :
- TicketsRepository (opérations CRUD implémentées)
- TicketHistoriqueRepository (opérations CRUD implémentées)
- CategorieRepository (opérations CRUD implémentées)

**v2 Repositories** :
- IssueRepository (opérations CRUD implémentées)
- JournalRepository (opérations CRUD implémentées)
- IssuePieceJointeRepository (opérations CRUD implémentées)
- IssuePrioriteRepository (opérations CRUD implémentées)
- MissionRepository (opérations CRUD implémentées)
- TypeMissionRepository (opérations CRUD implémentées)
- DomaineMetierRepository (opérations CRUD implémentées)
- PersonneRepository (opérations CRUD implémentées)
- TypePersonneRepository (opérations CRUD implémentées)

### 🆕 Nouvellement Implémentés

**Nouveaux Référentiels v2** :
- `JournalRepository` - Référentiel complet pour la gestion des journaux avec relations vers les Issues
- `IssuePieceJointeRepository` - Référentiel pour les pièces jointes avec support des fichiers et relations vers Issues/Journaux
- `IssuePrioriteRepository` - Référentiel pour les priorités d'issues avec relations vers les domaines métier
- `PersonneRepository` - Référentiel pour la gestion des personnes avec relations vers les types de personne
- `TypePersonneRepository` - Référentiel pour les types de personne avec relations vers les domaines métier

**Caractéristiques des nouveaux référentiels** :
- Implémentation complète de l'interface IRepository (GetAll, GetById, Add, Update, Delete, DeleteById)
- Gestion des relations entre entités avec méthodes helper privées
- Gestion appropriée des connexions MySQL avec ouverture/fermeture automatique
- Paramètres SQL sécurisés pour prévenir les injections SQL
- Gestion des valeurs nulles avec DBNull.Value
- Gestion d'erreurs avec try-catch-finally appropriée
- Support des relations many-to-many via tables de jonction

### 🚧 Partiellement Implémentés

**Composants ETL** :
- Définitions d'interface complètes
- Classes de transformateur créées mais méthodes non implémentées
- Classes d'extracteur créées mais méthodes non implémentées
- Classes de chargeur créées mais vides
- Classes de validateur créées mais vides

### ❌ Pas Encore Implémentés

**Logique ETL Principale** :
- Implémentations d'extraction réelles
- Algorithmes de transformation de données
- Procédures de chargement
- Règles de validation
- Orchestration ETL principale dans `Program.cs` (la méthode `Run()` retourne false)

**Fonctionnalités Supplémentaires** :
- Mécanismes de récupération d'erreur
- Rapports de progression
- Surveillance des performances
- Vérification de migration de données